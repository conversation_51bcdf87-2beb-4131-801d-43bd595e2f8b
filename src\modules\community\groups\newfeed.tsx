/* eslint-disable react-native/no-inline-styles */
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Text,
  TouchableOpacity,
} from 'react-native';
import {View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {useNavigation, useRoute} from '@react-navigation/native';
import React, {useCallback, useEffect, useRef} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {groupRole, StorageContanst} from '../../../Config/Contanst';
import store, {AppDispatch, RootState} from '../../../redux/store/store';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {DefaultPost} from '../card/defaultPost';
import {GroupPostsActions} from '../reducers/groupPostsReducer';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {myFeedActions} from '../reducers/MyFeedReducer';
import ConfigAPI from '../../../Config/ConfigAPI';

export default function NewFeed({
  groupId,
  role,
}: {
  groupId: string;
  role: number;
}) {
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const route = useRoute();
  const customer = useSelectorCustomerState().data;
  const size = 20;
  const groupPostsState = useSelector(
    (state: RootState) =>
      state.groupPosts.byGroupId[groupId] || {
        posts: [],
        isLoading: false,
        error: null,
        page: 1,
        hasMore: true,
      },
  );
  const {posts, isLoading, page, hasMore} = groupPostsState;

  useEffect(() => {
    loadPosts();
  }, [groupId]);
  const loadPosts = (refresh = true) => {
    if (refresh) {
      dispatch(GroupPostsActions.getGroupPosts(groupId, 1));
    } else if (hasMore && !isLoading) {
      dispatch(GroupPostsActions.getGroupPosts(groupId, page + 1));
    }
  };
  const handleLike = useCallback(
    async (postId: string, isLiked: boolean) => {
      const token = await getDataToAsyncStorage(StorageContanst.accessToken);
      if (token) {
        await dispatch(
          GroupPostsActions.updatePostLike(groupId, postId, isLiked),
        );
        var lstNews = store
          .getState()
          .newsFeed.data.find((item: any) => item.Id === postId);
        if (lstNews) {
          dispatch(newsFeedActions.setLike(lstNews.Id, !isLiked));
        }
      } else {
        dialogCheckAcc(dialogRef);
      }
    },
    [dispatch, groupId, route.name],
  );
  const handleBookmark = useCallback(
    async (postId: string, isBookmarked: boolean) => {
      const token = await getDataToAsyncStorage(StorageContanst.accessToken);
      if (token) {
        await dispatch(
          GroupPostsActions.toggleBookmark(groupId, postId, isBookmarked),
        );
        var lstNews = store
          .getState()
          .newsFeed.data.find((item: any) => item.Id === postId);
        if (lstNews) {
          dispatch(newsFeedActions.setBookmark(lstNews.Id, !isBookmarked));
        }
      } else {
        dialogCheckAcc(dialogRef);
      }
    },
    [dispatch, groupId, route.name],
  );
  const bottomSheetRef = useRef<any>(null);
  return (
    <View
      style={{
        flex: 1,
        padding: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        gap: 8,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <View
        style={{
          borderRadius: 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          height: 69,
          padding: 16,
          alignContent: 'center',
          justifyContent: 'center',
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: 16,
            width: '100%',
          }}>
          <View style={{width: 32, height: 32}}>
            <SkeletonImage
              key={customer?.AvatarUrl}
              source={{
                uri: customer
                  ? ConfigAPI.urlImg + customer?.AvatarUrl
                  : 'https://placehold.co/32',
              }}
              height={32}
              style={{height: 32, width: 32, borderRadius: 100}}
            />
          </View>
          <TouchableOpacity
            style={{
              flex: 1,
              height: 40,
              justifyContent: 'center',
              backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
              borderRadius: 8,
            }}
            onPress={() => {
              if (!customer) {
                dialogCheckAcc(dialogRef);
                return;
              }
              navigation.push(RootScreen.createPost, {groupId: groupId});
            }}>
            <Text
              style={{
                ...TypoSkin.placeholder1,
                color: ColorThemes.light.Neutral_Text_Color_Placeholder,
                paddingHorizontal: 12,
              }}>
              Bạn đang nghĩ gì ?
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* posts  */}
      <View style={{flex: 1}}>
        <FlatList
          data={posts}
          scrollEnabled={false}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            gap: 8,
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
          }}
          renderItem={({item, index}) => {
            return (
              <DefaultPost
                onPressDetail={() => {
                  navigate(RootScreen.PostDetail, {
                    item: {...item, isGroup: true},
                  });
                }}
                onPressHeader={() =>
                  navigate(RootScreen.ProfileCommunity, {
                    Id: item.CustomerId,
                  })
                }
                mainContainerStyle={{
                  flexDirection: 'row-reverse',
                  alignItems: 'flex-start',
                }}
                containerStyle={{
                  paddingHorizontal: 16,
                }}
                data={item}
                trailingView={
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 4,
                    }}>
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      borderColor="transparent"
                      onPress={() => {}}
                      containerStyle={{
                        borderRadius: 100,
                        padding: 6,
                        height: 24,
                        width: 24,
                      }}
                      title={
                        <Winicon
                          src={'outline/user interface/hyperlink'}
                          size={14}
                          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                        />
                      }
                    />
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      borderColor="transparent"
                      onPress={() => {
                        showBottomSheet({
                          ref: bottomSheetRef,
                          enableDismiss: true,
                          title: 'Actions',
                          suffixAction: <View />,
                          prefixAction: (
                            <TouchableOpacity
                              onPress={() => hideBottomSheet(bottomSheetRef)}
                              style={{padding: 6, alignItems: 'center'}}>
                              <Winicon
                                src="outline/layout/xmark"
                                size={20}
                                color={
                                  ColorThemes.light.Neutral_Text_Color_Body
                                }
                              />
                            </TouchableOpacity>
                          ),
                          children: (
                            <View
                              style={{
                                // gap: 4,
                                height: Dimensions.get('window').height / 4.5,
                                width: '100%',
                                backgroundColor:
                                  ColorThemes.light
                                    .Neutral_Background_Color_Absolute,
                              }}>
                              <ListTile
                                onPress={() => {
                                  showSnackbar({
                                    message: 'Chức năng đang được phát triển',
                                    status: ComponentStatus.WARNING,
                                  });
                                }}
                                title={'Report post'}
                                titleStyle={{...TypoSkin.body3}}
                              />
                              {item.CustomerId === customer.Id && (
                                <ListTile
                                  onPress={() => {
                                    hideBottomSheet(bottomSheetRef);
                                    // Alert.alert('Edit post');
                                    navigation.push(RootScreen.createPost, {
                                      editPost: item,
                                      groupId: groupId,
                                    });
                                    // showSnackbar({
                                    //   message: 'Chức năng đang được phát triển',
                                    //   status: ComponentStatus.WARNING,
                                    // });
                                  }}
                                  title={'Edit post'}
                                  titleStyle={{...TypoSkin.body3}}
                                />
                              )}
                              {(item.CustomerId === customer.Id ||
                                role === groupRole.admin ||
                                role === groupRole.subadmin) && (
                                <ListTile
                                  onPress={() => {
                                    hideBottomSheet(bottomSheetRef);
                                    dispatch(
                                      GroupPostsActions.deletePost(item, role),
                                    );
                                    const newfeed = store
                                      .getState()
                                      .newsFeed.data.find(
                                        (a: any) => a.Id === item.Id,
                                      );
                                    if (newfeed) {
                                      dispatch(
                                        newsFeedActions.hidePostNocall(
                                          newfeed.Id,
                                        ),
                                      );
                                    }
                                    const myfeed = store
                                      .getState()
                                      .myFeed.data.find(
                                        (a: any) => a.Id === item.Id,
                                      );
                                    if (myfeed) {
                                      dispatch(
                                        myFeedActions.hidePostNocall(myfeed.Id),
                                      );
                                    }
                                  }}
                                  title={'Delete post'}
                                  titleStyle={{...TypoSkin.body3}}
                                />
                              )}
                              {/* <ListTile
                                onPress={() => {
                                  hideBottomSheet(bottomSheetRef);
                                  Alert.alert('Edit post');
                                }}
                                title={'Hide post'}
                                titleStyle={{...TypoSkin.body3}}
                              /> */}
                            </View>
                          ),
                        });
                      }}
                      containerStyle={{
                        borderRadius: 100,
                        padding: 6,
                        height: 24,
                        width: 24,
                      }}
                      title={
                        <Winicon
                          src={'fill/user interface/menu-dots'}
                          size={14}
                          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                        />
                      }
                    />
                  </View>
                }
                actionView={
                  <View
                    style={{
                      flex: 1,
                      paddingTop: 16,
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      borderColor="transparent"
                      containerStyle={{
                        padding: 4,
                        height: 24,
                        paddingVertical: 0,
                        paddingHorizontal: 8,
                      }}
                      onPress={async () => {
                        handleLike(item.Id, item.IsLike === true);
                      }}
                      // containerStyle={{padding: 4}}
                      title={
                        <Text
                          style={{
                            ...TypoSkin.buttonText5,
                            color:
                              ColorThemes.light.Neutral_Text_Color_Subtitle,
                          }}>
                          {item.Likes ?? 0}
                        </Text>
                      }
                      textColor={
                        item.IsLike === true
                          ? ColorThemes.light.Error_Color_Main
                          : ColorThemes.light.Neutral_Text_Color_Subtitle
                      }
                      prefixIconSize={12}
                      prefixIcon={
                        item.IsLike === true
                          ? 'fill/emoticons/heart'
                          : 'outline/emoticons/heart'
                      }
                    />
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      borderColor="transparent"
                      containerStyle={{
                        padding: 4,
                        height: 24,
                        paddingVertical: 0,
                        paddingHorizontal: 8,
                      }}
                      onPress={async () => {
                        handleBookmark(item.Id, item.IsBookmark === true);
                      }}
                      prefixIcon={'outline/user interface/b-comment'}
                      prefixIconSize={12}
                      textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      title={
                        <Text
                          style={{
                            ...TypoSkin.buttonText5,
                            color:
                              ColorThemes.light.Neutral_Text_Color_Subtitle,
                          }}>
                          {item.Comment ?? 0}
                        </Text>
                      }
                    />
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      borderColor="transparent"
                      containerStyle={{
                        padding: 4,
                        height: 24,
                        paddingVertical: 0,
                        paddingHorizontal: 8,
                      }}
                      onPress={async () => {
                        handleBookmark(item.Id, item.IsBookmark === true);
                      }}
                      prefixIcon={'fill/arrows/social-sharing'}
                      prefixIconSize={12}
                      textColor={
                        item.IsBookmark === true
                          ? ColorThemes.light.Primary_Color_Main
                          : ColorThemes.light.Neutral_Text_Color_Subtitle
                      }
                    />
                  </View>
                }
              />
            );
          }}
          style={{width: '100%', height: '100%'}}
          keyExtractor={(item, index) => `${item.Id}-${index}`}
          onEndReachedThreshold={0.5}
          initialNumToRender={size}
          // onEndReached={() => {
          //   setPage(page + 1);
          //   dispatch(newsFeedActions.getNewFeedLoadmore(page,size));
          // }}
          ListEmptyComponent={() => {
            if (isLoading) {
              return (
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <ActivityIndicator
                    color={ColorThemes.light.Primary_Color_Main}
                  />
                </View>
              );
            }
            return <EmptyPage />;
          }}
        />
      </View>
    </View>
  );
}
