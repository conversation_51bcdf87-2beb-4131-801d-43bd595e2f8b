import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ViewStyle,
  TextStyle,
  ImageBackground,
} from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import {BackgroundData} from '../../../../redux/models/PostBackground';

interface ContentBackgroundProps {
  text: string;
  background: BackgroundData;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const ContentBackground: React.FC<ContentBackgroundProps> = ({
  text,
  background,
  style,
  textStyle,
}) => {
  if (!background) return null;

  // Xác định màu text - sử dụng TextColor nếu có, không thì dùng white mặc định
  const textColor = background.TextColor || 'white';

  switch (background.Type) {
    case 999: // Empty background
      return (
        <View style={[styles.container, {backgroundColor: 'white'}, style]}>
          <Text style={[styles.text, {color: 'black'}, textStyle]}>{text}</Text>
        </View>
      );

    case 1: // Image background
      return (
        <ImageBackground
          source={{uri: background.Img}}
          style={[styles.container, style]}
          imageStyle={{borderRadius: 8}}
          resizeMode="cover">
          <View style={styles.imageOverlay}>
            <Text style={[styles.text, {color: textColor}, textStyle]}>
              {text}
            </Text>
          </View>
        </ImageBackground>
      );

    case 2: // Color background (Solid or Gradient)
      const colors = background.ColorMobile
        ? background.ColorMobile.split(',')
            .map(c => c.trim())
            .filter(Boolean)
            .filter(color => {
              // Kiểm tra định dạng màu cơ bản
              return /^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{3}$|^rgb\(|^rgba\(|^[a-zA-Z]+$/.test(
                color,
              );
            })
        : [];

      if (colors.length === 0) {
        return (
          <View style={[styles.container, {backgroundColor: '#6A0DAD'}, style]}>
            <Text style={[styles.text, {color: textColor}, textStyle]}>
              {text}
            </Text>
          </View>
        );
      }

      if (colors.length < 2) {
        // Solid color
        const singleColor = colors[0];
        if (!singleColor) {
          return (
            <View
              style={[styles.container, {backgroundColor: '#6A0DAD'}, style]}>
              <Text style={[styles.text, {color: textColor}, textStyle]}>
                {text}
              </Text>
            </View>
          );
        }
        return (
          <View
            style={[styles.container, {backgroundColor: singleColor}, style]}>
            <Text style={[styles.text, {color: textColor}, textStyle]}>
              {text}
            </Text>
          </View>
        );
      } else {
        // Gradient
        const validColors = colors.slice(0, 10); // Giới hạn số màu để tránh performance issues
        return (
          <LinearGradient
            colors={validColors}
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            style={[
              {
                borderRadius: 8,
              },
            ]}>
            <View style={{...styles.container, ...(style ?? {})}}>
              <Text style={[styles.text, {color: textColor}, textStyle]}>
                {text}
              </Text>
            </View>
          </LinearGradient>
        );
      }

    default:
      return (
        <View style={[styles.container, style]}>
          <Text style={[styles.text, textStyle]}>{text}</Text>
        </View>
      );
  }
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    minHeight: 200,
    padding: 16,
  },
  text: {
    width: '100%',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Overlay tối nhẹ để text dễ đọc trên background image
    width: '100%',
    borderRadius: 8,
    padding: 16,
  },
});

export default ContentBackground;
