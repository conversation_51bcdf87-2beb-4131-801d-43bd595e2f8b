import {createSlice, PayloadAction, Dispatch} from '@reduxjs/toolkit';
import store, {AppDispatch} from '../../../redux/store/store';
import {ThunkAction} from 'redux-thunk';
import {RootState} from '../../../redux/store/store';
import {Action} from 'redux';
import {DataController} from '../../../base/baseController';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {
  groupMemberRoleStatus,
  groupRole,
  StorageContanst,
} from '../../../Config/Contanst';
import {randomGID, Ultis} from '../../../utils/Utils';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {newsFeedActions} from './newsFeedReducer';
import {fetchPostBackground} from '../../../redux/actions/PostBackgroundAction';

interface GroupPostsState {
  byGroupId: {
    [groupId: string]: {
      posts: any[];
      isLoading: boolean;
      error: string | null;
      page: number;
      hasMore: boolean;
    };
  };
}

const initialState: GroupPostsState = {
  byGroupId: {},
};

// Thêm constant cho action UPDATE_POST
export const UPDATE_POST = 'UPDATE_POST';

const groupPostsSlice = createSlice({
  name: 'groupPosts',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<any>) => {
      if (!state.byGroupId[action.payload.groupId]) {
        state.byGroupId[action.payload.groupId] = {
          posts: [],
          isLoading: false,
          error: null,
          page: 1,
          hasMore: true,
        };
      }
      state.byGroupId[action.payload.groupId].isLoading =
        action.payload.isLoading;
    },
    setPosts: (
      state,
      action: PayloadAction<{groupId: string; posts: []; page: number}>,
    ) => {
      state.byGroupId[action.payload.groupId] = {
        posts: action.payload.posts,
        isLoading: false,
        error: null,
        page: action.payload.page,
        hasMore: action.payload.posts.length > 0,
      };
    },
    addPosts: (state, action: PayloadAction<any>) => {
      state.byGroupId[action.payload.groupId] = {
        posts: [
          action.payload.posts,
          ...state.byGroupId[action.payload.groupId].posts,
        ],
        isLoading: false,
        error: null,
        page: action.payload.page,
        hasMore: action.payload.posts.length > 0,
      };
    },
    appendPosts: (
      state,
      action: PayloadAction<{groupId: string; posts: []; page: number}>,
    ) => {
      if (!state.byGroupId[action.payload.groupId]) {
        state.byGroupId[action.payload.groupId] = {
          posts: [],
          isLoading: false,
          error: null,
          page: 1,
          hasMore: true,
        };
      }
      state.byGroupId[action.payload.groupId].posts.push(
        ...action.payload.posts,
      );
      state.byGroupId[action.payload.groupId].page = action.payload.page;
      state.byGroupId[action.payload.groupId].hasMore =
        action.payload.posts.length > 0;
    },
    setError: (
      state,
      action: PayloadAction<{groupId: string; error: string}>,
    ) => {
      if (!state.byGroupId[action.payload.groupId]) {
        state.byGroupId[action.payload.groupId] = {
          posts: [],
          isLoading: false,
          error: null,
          page: 1,
          hasMore: true,
        };
      }
      state.byGroupId[action.payload.groupId].error = action.payload.error;
      state.byGroupId[action.payload.groupId].isLoading = false;
    },
    updateLike: (
      state,
      action: PayloadAction<{
        groupId: string;
        PostsId: string;
        isLike: boolean;
      }>,
    ) => {
      const {groupId, PostsId, isLike} = action.payload;
      const groupPosts = state.byGroupId[groupId];
      if (groupPosts) {
        const post = groupPosts.posts.find(p => p.Id === PostsId);
        if (post) {
          post.IsLike = isLike;
          post.Likes += isLike ? 1 : -1;
        }
      }
    },
    updateCommentCount: (
      state,
      action: PayloadAction<{
        groupId: string;
        PostsId: string;
      }>,
    ) => {
      const {groupId, PostsId} = action.payload;
      const groupPosts = state.byGroupId[groupId];
      if (groupPosts) {
        const post = groupPosts.posts.find(p => p.Id === PostsId);
        if (post) {
          post.Comment += 1;
        }
      }
    },
    updateBookmark: (
      state,
      action: PayloadAction<{
        groupId: string;
        PostsId: string;
        isBookmark: boolean;
      }>,
    ) => {
      const {groupId, PostsId, isBookmark} = action.payload;
      const groupPosts = state.byGroupId[groupId];
      if (groupPosts) {
        const post = groupPosts.posts.find(p => p.Id === PostsId);
        if (post) {
          post.IsBookmark = isBookmark;
        }
      }
    },
    hidePost: (state, action: PayloadAction<any>) => {
      const {groupId, postId} = action.payload;
      const groupPosts = state.byGroupId[groupId];
      if (groupPosts) {
        groupPosts.posts = groupPosts.posts.filter(p => p.Id !== postId);
      }
    },
    resetGroupPosts: (state, action: PayloadAction<string>) => {
      delete state.byGroupId[action.payload];
    },
    // Thêm reducer để cập nhật bài đăng
    updatePost: (
      state,
      action: PayloadAction<{groupId: string; post: any}>,
    ) => {
      const {groupId, post} = action.payload;

      // Kiểm tra xem group có tồn tại trong state không
      if (state.byGroupId[groupId]) {
        // Cập nhật bài đăng trong danh sách posts của group
        state.byGroupId[groupId].posts = state.byGroupId[groupId].posts.map(
          existingPost =>
            existingPost.Id === post.Id
              ? {
                  ...existingPost,
                  Content: post.Content,
                  Img: post.Img,
                  DateModified: post.DateModified,
                }
              : existingPost,
        );
      }
    },
  },
});

export const {
  setLoading,
  setPosts,
  addPosts,
  hidePost,
  updateCommentCount,
  appendPosts,
  setError,
  updateLike,
  updateBookmark,
  resetGroupPosts,
  updatePost,
} = groupPostsSlice.actions;

export class GroupPostsActions {
  static getGroupPosts =
    (
      groupId: string,
      page: number = 1,
      size: number = 10,
    ): ThunkAction<Promise<void>, RootState, unknown, Action<string>> =>
    async dispatch => {
      try {
        dispatch(setLoading({groupId, isLoading: true}));
        const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

        // 1. Get posts
        const postController = new DataController('Posts');
        const result = await postController.getListSimple({
          query: `@GroupId:{${groupId}} -@IsHidden:{true}`,
          page,
          size,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (result.code !== 200 || !result.data.length) {
          dispatch(setError({groupId, error: 'Failed to fetch posts'}));
          return;
        }

        // 2. Get all required IDs
        const PostsIds = result.data.map((post: any) => post.Id).join(' | ');
        const customerIds = [
          ...new Set(result.data.map((post: any) => post.CustomerId)),
        ].join(' | ');

        // 3. Fetch all related data in parallel
        const [likesData, commentsData, bookmarks, customers] =
          await Promise.all([
            // Get likes count and user's like status in one query
            // new DataController('Likes').group({
            //   searchRaw: `@PostsId:{${PostsIds}}`,
            //   reducers: `
            //     GROUPBY 1 @PostsId
            //     REDUCE COUNT 0 AS LikeCount
            //     ${cusId ? `REDUCE COUNT_DISTINCT @CustomerId:${cusId} AS UserLiked` : ''}
            //   `
            // }),
            new DataController('Likes').getListSimple({
              query: `@PostsId:{${PostsIds}}`,
              size: 0, // Get total count only
            }),

            // Get comments count in one query
            new DataController('Comments').group({
              searchRaw: `@PostsId:{${PostsIds}}`,
              reducers: 'GROUPBY 1 @PostsId REDUCE COUNT 0 AS CommentCount',
            }),

            // Get bookmarks with minimal fields
            cusId
              ? new DataController('Post_Bookmark').getListSimple({
                  query: `@CustomerId:{${cusId}} @PostsId:{${PostsIds}}`,
                  returns: ['PostsId'], // Chỉ lấy PostsId field
                })
              : Promise.resolve({data: []}),

            // Get customer details with specific fields
            new DataController('Customer').getListSimple({
              query: `@Id:{${customerIds}}`,
              returns: ['Id', 'Name', 'AvatarUrl'], // Chỉ lấy các field cần thiết
            }),
          ]);

        // 4. Create efficient lookup maps
        const commentsMap = new Map(
          commentsData.data?.map((item: any) => [
            item.PostsId,
            item.CommentCount || 0,
          ]),
        );
        const bookmarksSet = new Set(
          bookmarks.data?.map((b: any) => b.PostsId),
        );
        const customersMap = new Map(
          customers.data?.map((user: any) => [user.Id, user]),
        );

        // 5. Enrich posts with all data
        let enrichedPosts = result.data.map((post: any) => {
          const likeInfo = likesData.data.filter(
            (l: any) => l.PostsId === post.Id,
          );
          const customer = customersMap.get(post.CustomerId) as any;

          return {
            ...post,
            Likes: likeInfo?.length || 0,
            IsLike: cusId
              ? likeInfo.some((like: any) => like.CustomerId === cusId)
              : false,
            Comment: commentsMap.get(post.Id) || 0,
            IsBookmark: bookmarksSet.has(post.Id),
            relativeUser: customer
              ? {
                  image: customer.AvatarUrl,
                  title: customer.Name,
                  subtitle: Ultis.getDiffrentTime(post.DateCreated),
                }
              : {
                  image: '',
                  title: 'Unknown User',
                  subtitle: Ultis.getDiffrentTime(post.DateCreated),
                },
          };
        });

        enrichedPosts = await fetchPostBackground(enrichedPosts);

        // 6. Update state
        dispatch(
          page === 1
            ? setPosts({groupId, posts: enrichedPosts, page})
            : appendPosts({groupId, posts: enrichedPosts, page}),
        );
      } catch (error) {
        dispatch(
          setError({
            groupId,
            error: error instanceof Error ? error.message : 'An error occurred',
          }),
        );
      }
    };

  static addPost = (postData: any) => async (dispatch: Dispatch) => {
    const customer = store.getState().customer.data;
    if (!customer) {
      return false;
    }
    try {
      dispatch(setLoading({groupId: postData.GroupId, isLoading: true}));
      const postController = new DataController('Posts');

      const dataCreate = {
        ...postData,
      };
      delete dataCreate.PostBackgroundM;

      const result = await postController.add([dataCreate]);
      if (result.code === 200) {
        const postWithUser = {
          ...postData,
          Likes: 0,
          IsLike: false,
          Comment: 0,
          IsBookmark: false,
          relativeUser: {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: 'Just now',
          },
        };
        dispatch(addPosts({groupId: postData.GroupId, posts: postWithUser}));
        return true;
      }
      return false;
    } catch (error) {
      dispatch(
        setError({
          groupId: postData.GroupId,
          error: error instanceof Error ? error.message : 'An error occurred',
        }),
      );
      return false;
    }
  };
  static hidePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Posts');
      var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể ẩn
      //kiểm tra user có phải là admin hoặc subadmin của group không.
      const groupMemberController = new DataController('Role');
      const memberResult = await groupMemberController.getListSimple({
        query: `@GroupId:{${post.GroupId}} @CustomerId:{${cusId}} @Status:[${groupMemberRoleStatus.joined}]`,
      });
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể ẩn
      //kiểm tra user có phải là admin hoặc subadmin của group không.
      if (memberResult.code === 200 && memberResult.data.length > 0) {
        const member = memberResult.data[0];
        if (
          member.Type === groupRole.admin ||
          member.Type === groupRole.subadmin ||
          post.CustomerId === cusId
        ) {
          // Cập nhật trạng thái IsHidden của bài đăng
          const updateResult = await postController.edit([
            {
              ...post,
              IsHidden: true,
            },
          ]);

          if (updateResult.code === 200) {
            // Cập nhật state trong Redux
            dispatch(hidePost({groupId: post.GroupId, postId: post.Id}));
            return true;
          }
          showSnackbar({
            message: 'Bạn không có quyền ẩn bài đăng này',
            status: ComponentStatus.ERROR,
          });
          return false;
        }
      }
      showSnackbar({
        message: 'Bạn không có quyền ẩn bài đăng này',
        status: ComponentStatus.ERROR,
      });
      return false;
    } catch (error) {
      console.error('Error hiding post:', error);
      return false;
    }
  };
  static deletePost =
    (post: any, role: number) => async (dispatch: Dispatch) => {
      try {
        const postController = new DataController('Posts');
        var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
        if (!cusId) {
          console.error('User not authenticated');
          return false;
        }
        // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể xóa
        // const post = await postController.getById(postId);
        if (
          !post ||
          (post.CustomerId !== cusId &&
            role !== groupRole.admin &&
            role !== groupRole.subadmin)
        ) {
          // console.error('Not authorized to delete this post');
          showSnackbar({
            message: 'Bạn không có quyền xóa bài đăng này',
            status: ComponentStatus.ERROR,
          });
          return false;
        }
        const deleteResult = await postController.delete([post.Id]);
        if (deleteResult.code === 200) {
          dispatch(hidePost({groupId: post.GroupId, postId: post.Id}));
          return true;
        }
        return false;
      } catch (error) {
        console.error('Error deleting post:', error);
        return false;
      }
    };
  static hidePostNocall =
    (groupId: string, postId: any) => async (dispatch: Dispatch) => {
      dispatch(hidePost({groupId: groupId, postId: postId}));
    };

  static updatePostLike =
    (
      groupId: string,
      PostsId: string,
      isLiked: boolean,
    ): ThunkAction<Promise<boolean>, RootState, unknown, Action<string>> =>
    async dispatch => {
      try {
        const likeController = new DataController('Likes');
        const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

        if (!cusId) return false;
        if (isLiked) {
          // Unlike - Delete existing like
          const result = await likeController.getListSimple({
            query: `@CustomerId: {${cusId}} @PostsId:{${PostsId}}`,
          });

          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(updateLike({groupId, PostsId, isLike: false}));
              return true;
            }
          }
        } else {
          // Like - Add new like
          const data = {
            Id: randomGID(),
            CustomerId: cusId,
            PostsId: PostsId,
            DateCreated: new Date().getTime(),
          };
          const result = await likeController.add([data]);
          if (result.code === 200) {
            dispatch(updateLike({groupId, PostsId, isLike: true}));
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error('Error updating like:', error);
        return false;
      }
    };
  static setLike =
    (id: string, isUnLike: boolean, groupId: string) =>
    async (dispatch: Dispatch) => {
      dispatch(updateLike({groupId, PostsId: id, isLike: isUnLike}));
    };
  static setBookmark =
    (id: string, IsBookmark: boolean, groupId: string) =>
    async (dispatch: Dispatch) => {
      dispatch(updateBookmark({groupId, PostsId: id, isBookmark: IsBookmark}));
    };
  static resetPosts =
    (groupId: string): ThunkAction<void, RootState, unknown, Action<string>> =>
    dispatch => {
      dispatch(resetGroupPosts(groupId));
    };

  static addBookmark =
    (
      groupId: string,
      PostsId: string,
    ): ThunkAction<Promise<boolean>, RootState, unknown, Action<string>> =>
    async dispatch => {
      try {
        const bookmarkController = new DataController('Post_Bookmark');
        const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

        if (!cusId) return false;

        const data = {
          Id: randomGID(),
          CustomerId: cusId,
          PostsId: PostsId,
          DateCreated: new Date().getTime(),
        };

        const result = await bookmarkController.add([data]);
        if (result.code === 200) {
          dispatch(updateBookmark({groupId, PostsId, isBookmark: true}));
          return true;
        }
        return false;
      } catch (error) {
        console.error('Error adding bookmark:', error);
        return false;
      }
    };

  static removeBookmark =
    (
      groupId: string,
      PostsId: string,
    ): ThunkAction<Promise<boolean>, RootState, unknown, Action<string>> =>
    async dispatch => {
      try {
        const bookmarkController = new DataController('Post_Bookmark');
        const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

        if (!cusId) return false;

        const result = await bookmarkController.getListSimple({
          query: `@CustomerId: {${cusId}} @PostsId:{${PostsId}}`,
        });

        if (result.data?.length > 0) {
          const unbookmark = await bookmarkController.delete([
            result.data[0].Id,
          ]);
          if (unbookmark.code === 200) {
            dispatch(updateBookmark({groupId, PostsId, isBookmark: false}));
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error('Error removing bookmark:', error);
        return false;
      }
    };

  static updateCommentCount =
    (
      groupId: string,
      PostsId: string,
    ): ThunkAction<Promise<boolean>, RootState, unknown, Action<string>> =>
    async dispatch => {
      dispatch(GroupPostsActions.updateCommentCount(groupId, PostsId));
      return true;
    };
  static toggleBookmark =
    (
      groupId: string,
      PostsId: string,
      isCurrentlyBookmarked: boolean,
    ): ThunkAction<Promise<boolean>, RootState, unknown, Action<string>> =>
    async dispatch => {
      if (isCurrentlyBookmarked) {
        return dispatch(GroupPostsActions.removeBookmark(groupId, PostsId));
      } else {
        return dispatch(GroupPostsActions.addBookmark(groupId, PostsId));
      }
    };

  static updatePost =
    (
      post: any,
    ): ThunkAction<Promise<boolean>, RootState, unknown, Action<string>> =>
    async dispatch => {
      try {
        // Lấy groupId từ post
        const groupId = post.GroupId;

        if (!groupId) {
          console.error('GroupId is missing in the post data');
          return false;
        }

        // Dispatch action để cập nhật post trong Redux store
        dispatch(updatePost({groupId, post}));

        // Cập nhật cả trong newsFeed nếu bài đăng cũng hiển thị ở đó
        const storeState = store.getState();
        const postInNewsFeed = storeState.newsFeed.data.find(
          (item: any) => item.Id === post.Id,
        );

        if (postInNewsFeed) {
          dispatch(newsFeedActions.updatePost(post));
        }

        return true;
      } catch (error) {
        console.error('Error updating post in Redux store:', error);
        return false;
      }
    };
}

export default groupPostsSlice.reducer;
