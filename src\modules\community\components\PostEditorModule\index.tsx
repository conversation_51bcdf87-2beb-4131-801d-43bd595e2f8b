/* eslint-disable react-native/no-inline-styles */
import React, {
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useState,
} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import {useTextSegments} from './hooks/useTextSegments';
import {useImagePicker} from './hooks/useImagePicker';
import {segmentsToHTML, getFullText} from './utils/textUtils';
import ImagePreview from './ImagePreview';
import Toolbar from './Toolbar';
import {
  RichTextComposerProps,
  RichTextComposerRef,
  PostData,
  ImageItem,
} from './types';
import {styles} from './styles';
import {ColorThemes} from '../../../../assets/skin/colors';
import ConfigAPI from '../../../../Config/ConfigAPI';
import PickColorLine from './PickColorLine';
import EditorBackground from './EditorBackgound';
import {BackgroundData} from '../../../../redux/models/PostBackground';

const RichTextComposer = forwardRef<RichTextComposerRef, RichTextComposerProps>(
  (props, ref) => {
    const {
      onTextChange,
      onImagesChange,
      onDataChange,
      initialText = '',
      initialImages = [],
      initialHtml = '',
      initialImageIds = [],
      maxImages = 10,
    } = props;

    const inputRef = useRef<TextInput>(null);
    const [isResetEditorBackground, setIsResetEditorBackground] =
      useState(false);
    const [backgroundData, setBackgroundData] = useState<BackgroundData>({
      Id: '0',
      Type: 999,
      Img: '',
      ColorMobile: '',
      TextColor: '',
    });

    // Use custom hooks
    const {
      segments,
      setSegments,
      format,
      selection,
      handleSelectionChange,
      toggleFormat,
      onChangeText,
    } = useTextSegments(initialText, initialHtml);

    const {
      selectedImages,
      setSelectedImages,
      handlePickImages,
      handleRemoveImage,
      handleRemoveAllImages,
    } = useImagePicker(initialImages, initialImageIds, maxImages);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      getPostData: () => {
        const data: any = {
          text: getFullText(segments),
          segments: segments,
          images: selectedImages,
          html: segmentsToHTML(segments),
          existingImageIds: selectedImages
            .filter(img => img.existingId)
            .map(img => img.existingId as string),
        };
        if (backgroundData.Type !== 999) data.backgroundData = backgroundData;
        return data;
      },
      clearContent: () => {
        setSegments([{text: '', bold: false, italic: false, underline: false}]);
        setSelectedImages([]);
        setBackgroundData({
          Id: '0',
          Type: 999,
          Img: '',
          ColorMobile: '',
          TextColor: '',
        });
      },
      setContent: (
        text: string,
        images: ImageItem[] = [],
        html?: string,
        imageIds?: string[],
      ) => {
        if (html) {
          // Import htmlToSegments from textUtils
          const {htmlToSegments} = require('./utils/textUtils');
          setSegments(htmlToSegments(html));
        } else {
          setSegments([{text, bold: false, italic: false, underline: false}]);
        }

        if (images.length > 0) {
          setSelectedImages(images);
        } else if (imageIds && imageIds.length > 0) {
          setSelectedImages(
            imageIds.map((id, index) => ({
              id: `existing-${index}`,
              uri: `${ConfigAPI.urlImg}${id}`,
              path: `${ConfigAPI.urlImg}${id}`,
              mime: 'image/jpeg',
              filename: `image-${index}.jpg`,
              existingId: id,
            })),
          );
        } else {
          setSelectedImages([]);
        }
      },
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    // Thêm useRef để theo dõi dữ liệu trước đó
    const prevDataRef = useRef<PostData | null>(null);

    // thông báo khi text hoặc ảnh thay đổi
    useEffect(() => {
      const fullText = getFullText(segments);

      // Tạo dữ liệu mới
      const newData = {
        text: fullText,
        segments: segments,
        images: selectedImages,
      };

      // Kiểm tra xem dữ liệu có thay đổi không
      const prevData = prevDataRef.current;
      const hasChanged =
        !prevData ||
        prevData.text !== newData.text ||
        prevData.images !== newData.images;

      // Chỉ gọi callbacks khi dữ liệu thực sự thay đổi
      if (hasChanged) {
        if (onTextChange) {
          onTextChange(fullText);
        }

        if (onImagesChange) {
          onImagesChange(selectedImages);
        }

        if (onDataChange) {
          onDataChange(newData);
        }

        // Cập nhật ref
        prevDataRef.current = newData;
      }
    }, [segments, selectedImages, onTextChange, onImagesChange, onDataChange]);

    // theo dõi selectedImages
    useEffect(() => {
      if (selectedImages.length > 0) {
        setIsResetEditorBackground(true);
        setBackgroundData({
          Id: '0',
          Type: 999,
          Img: '',
          ColorMobile: '',
          TextColor: '',
        });
      } else {
        setIsResetEditorBackground(false);
      }
    }, [selectedImages]);

    const fullText = getFullText(segments);

    // Focus lại vào input sau khi thay đổi định dạng
    const handleToggleFormat = (key: 'bold' | 'italic' | 'underline') => {
      toggleFormat(key);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    };

    // Chọn màu nền cho bài post
    const handleChooseBackgroundData = (color: BackgroundData) => {
      handleRemoveAllImages();
      setBackgroundData(color);
    };

    const renderEditorDefault = () => {
      return (
        <View style={styles.editorContainer}>
          <ScrollView
            style={styles.scrollArea}
            keyboardShouldPersistTaps="always">
            <Text style={styles.placeholder}>
              {fullText.length === 0 ? 'Bạn đang nghĩ gì ?' : ''}
            </Text>
            <View style={{position: 'relative'}}>
              <Text style={styles.richText}>
                {segments.map((seg, idx) => (
                  <Text
                    key={idx}
                    style={{
                      fontWeight: seg.bold ? 'bold' : 'normal',
                      fontStyle: seg.italic ? 'italic' : 'normal',
                      textDecorationLine: seg.underline ? 'underline' : 'none',
                      fontSize: 16,
                      lineHeight: 24,
                    }}>
                    {seg.text}
                  </Text>
                ))}
              </Text>
              <TextInput
                ref={inputRef}
                value={fullText}
                onChangeText={onChangeText}
                onSelectionChange={handleSelectionChange}
                multiline
                selection={selection}
                cursorColor={ColorThemes.light.Neutral_Text_Color_Body}
                style={[styles.richText, styles.hiddenInput]}
                caretHidden={false}
                autoCorrect={false}
                autoCapitalize="none"
                keyboardType="default"
              />
            </View>
          </ScrollView>
        </View>
      );
    };

    const renderEditorBackground = () => {
      return (
        <EditorBackground
          background={backgroundData}
          onChangeText={onChangeText}
        />
      );
    };

    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={{flex: 1}}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}>
          {/* Editor */}
          {backgroundData.Type === 999
            ? renderEditorDefault()
            : renderEditorBackground()}

          {/* Pick Color Line */}
          <PickColorLine
            onChoose={handleChooseBackgroundData}
            isReset={isResetEditorBackground}
          />

          {/* Image Preview Section */}
          <ImagePreview
            selectedImages={selectedImages}
            handleRemoveImage={handleRemoveImage}
            handlePickImages={handlePickImages}
            maxImages={maxImages}
          />
          {/* Toolbar */}
          <Toolbar
            format={format}
            toggleFormat={handleToggleFormat}
            handlePickImages={handlePickImages}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  },
);

export default RichTextComposer;
